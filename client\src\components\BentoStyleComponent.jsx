'use client';

import React from 'react';

const BentoStyleComponent = () => {
  const locations = [
    'Hangzhou, China',
    'Paris, France',
    'Munich, Germany',
    'Tokyo, Japan',
    'Seoul, Korea',
    'London, UK',
    'New York, US'
  ];

  return (
    <div className="w-full bg-[#FAF5F5] py-10">
      <div className="max-w-7xl mx-auto px-6 md:px-6 lg:px-8">
        {/* Centered heading and description */}
      <div className="text-center p-12">
        <h1 className="text-xl md:text-2xl lg:text-4xl font-bold text-gray-900 mb-4 md:mb-6">
          Empowering businesses through global trade
        </h1>
        <p className="text-base md:text-md lg:text-md text-gray-600 max-w-4xl mx-auto leading-relaxed font-medium">
          Alicartify offers one-stop B2C trading solutions for global small and medium-sized businesses, empowering them to transform through digital trade, grasp opportunities, and accelerate growth internationally.
        </p>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 md:gap-8">
        
        {/* Mission Section - Left side */}
        <div className="relative overflow-hidden rounded-2xl md:rounded-3xl min-h-[400px] md:min-h-[500px] flex flex-col justify-end">
          {/* Background image */}
          <div 
            className="absolute inset-0 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: 'url("https://i.ibb.co/fRSqHY3/dl-beatsnoop-com-3000-g-Ta-Ihs-OCUt.jpg")'
            }}
          ></div>
          
          {/* Dark overlay */}
          <div className="absolute inset-0 bg-black/40"></div>
          
          {/* Content */}
          <div className="relative z-10 p-6 md:p-8">
            <div className="mb-4">
              <span className="inline-block px-3 py-1 text-xs md:text-sm font-semibold text-white bg-white/20 backdrop-blur-sm rounded-full uppercase tracking-wide">
                Our Mission
              </span>
            </div>
            <h2 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white leading-tight">
              Make it easy to do business anywhere.
            </h2>
          </div>
        </div>

        {/* Right side container */}
        <div className="flex flex-col gap-6 md:gap-8">
          
          {/* Locations Section - Top right */}
          <div className="relative overflow-hidden rounded-2xl md:rounded-3xl min-h-[240px] md:min-h-[280px]">
            {/* World map background */}
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: 'url("https://i.ibb.co/4n7nn2hS/hero.jpg")'
              }}
            ></div>
            
            {/* Dark overlay */}
            <div className="absolute inset-0 bg-black/50"></div>
            
            <div className="relative z-10 p-6 md:p-8 h-full flex flex-col justify-end">
              <div className="mb-4">
                <span className="inline-block px-3 py-1 text-xs md:text-sm font-semibold text-white bg-white/20 backdrop-blur-sm rounded-full uppercase tracking-wide">
                  Our Locations
                </span>
              </div>
              <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-4 leading-tight">
                We have teams around the world.
              </h3>
              
              <div className="space-y-1">
                {locations.slice(0, 5).map((location, index) => (
                  <div key={index} className="text-sm md:text-base text-white font-medium">
                    {location}
                  </div>
                ))}
                <div className="text-sm md:text-base text-white/80 italic">
                  and many other locations worldwide.
                </div>
              </div>
            </div>
          </div>

          {/* ESG Promises Section - Bottom right */}
          <div className="relative overflow-hidden rounded-2xl md:rounded-3xl min-h-[200px] md:min-h-[220px]">
            {/* Background image */}
            <div 
              className="absolute inset-0 bg-cover bg-center bg-no-repeat"
              style={{
                backgroundImage: 'url("https://i.ibb.co/Z1WzF2dJ/3.jpg")'
              }}
            ></div>
            
            {/* Dark overlay */}
            <div className="absolute inset-0 bg-black/40"></div>
            
            <div className="relative z-10 p-6 md:p-8 h-full flex flex-col justify-end">
              <div className="mb-4">
                <span className="inline-block px-3 py-1 text-xs md:text-sm font-semibold text-white bg-white/20 backdrop-blur-sm rounded-full uppercase tracking-wide">
                  Our ESG Promises
                </span>
              </div>
              <h3 className="text-xl md:text-2xl lg:text-3xl font-bold text-white leading-tight">
                Responsible technology. Sustainable future.
              </h3>
            </div>
          </div>
        </div>
      </div>
      </div>
    </div>
  );
};

export default BentoStyleComponent;
